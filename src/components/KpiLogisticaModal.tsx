"use client";

import React, { useState, useEffect } from "react";
import { X, Save, Loader2 } from "lucide-react";
import confetti from "canvas-confetti";
import { getWeekInfo, canAddDataForWeek, getWeekDataRestrictionMessage, getLastCompletedWeekInfo, type WeekInfo } from "@/lib/utils/weekUtils";
import { type KpiLogisticaData } from "@/app/actions/kpis-logistica";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import AlertDialog from "@/components/ui/AlertDialog";

// Interfaz para el formulario que permite valores string o number
interface FormKpiLogisticaData extends Omit<KpiLogisticaData, 'unidadesConfirmadas' | 'unidadesSolicitadas' | 'porcentajeEntregasTiempo' | 'porcentajeRetardos' | 'porcentajeReprogramaciones' | 'promedioKmOperacion' | 'promedioCostoFleteLitro' | 'promedioCostoFleteOperacion' | 'pagoSemanalFlete' | 'pagoSemanalPenalizaciones' | 'porcentajeRutasCotizadas' | 'porcentajeTransportistas'> {
  unidadesConfirmadas: number | string;
  unidadesSolicitadas: number | string;
  porcentajeEntregasTiempo: number | string;
  porcentajeRetardos: number | string;
  porcentajeReprogramaciones: number | string;
  promedioKmOperacion: number | string;
  promedioCostoFleteLitro: number | string;
  promedioCostoFleteOperacion: number | string;
  pagoSemanalFlete: number | string;
  pagoSemanalPenalizaciones: number | string;
  porcentajeRutasCotizadas: number | string;
  porcentajeTransportistas: number | string;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceAbove = container.top;
    const spaceBelow = viewport.height - container.bottom;
    const tooltipHeight = tooltip.height || 100; // Estimación si no está renderizado

    const shouldShowAbove = spaceAbove > tooltipHeight + 10 && spaceBelow < tooltipHeight + 10;

    // Determinar posición horizontal
    const spaceLeft = container.left;
    const spaceRight = viewport.width - container.right;
    const tooltipWidth = tooltip.width || 200; // Estimación si no está renderizado

    let horizontalPosition = { left: false, right: false, center: true };

    if (spaceLeft < tooltipWidth / 2 && spaceRight > tooltipWidth) {
      horizontalPosition = { left: true, right: false, center: false };
    } else if (spaceRight < tooltipWidth / 2 && spaceLeft > tooltipWidth) {
      horizontalPosition = { left: false, right: true, center: false };
    }

    setPosition({
      top: !shouldShowAbove,
      ...horizontalPosition
    });
  }, []);

  useEffect(() => {
    if (isVisible) {
      updatePosition();
      window.addEventListener('scroll', updatePosition);
      window.addEventListener('resize', updatePosition);
      return () => {
        window.removeEventListener('scroll', updatePosition);
        window.removeEventListener('resize', updatePosition);
      };
    }
  }, [isVisible, updatePosition]);

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={`absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg whitespace-nowrap max-w-xs ${
            position.top ? 'bottom-full mb-2' : 'top-full mt-2'
          } ${
            position.left ? 'left-0' : position.right ? 'right-0' : 'left-1/2 transform -translate-x-1/2'
          }`}
          style={{
            fontSize: '12px',
            lineHeight: '1.4',
            maxWidth: '280px',
            whiteSpace: 'normal'
          }}
        >
          {content}
          <div
            className={`absolute w-2 h-2 bg-gray-900 transform rotate-45 ${
              position.top ? 'top-full -mt-1' : 'bottom-full -mb-1'
            } ${
              position.left ? 'left-3' : position.right ? 'right-3' : 'left-1/2 -translate-x-1/2'
            }`}
          />
        </div>
      )}
    </div>
  );
};

interface KpiLogisticaModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: KpiLogisticaData) => Promise<void>;
  editingKpi?: KpiLogisticaData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: any[];
}

const KpiLogisticaModal: React.FC<KpiLogisticaModalProps> = ({
  isOpen,
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = []
}) => {
  const [loading, setLoading] = useState(false);
  const [selectedWeek, setSelectedWeek] = useState<WeekInfo | null>(null);
  const [showWeekSelector, setShowWeekSelector] = useState(false);
  const [availableWeeks, setAvailableWeeks] = useState<WeekInfo[]>([]);
  const [showAlert, setShowAlert] = useState(false);
  const [alertConfig, setAlertConfig] = useState({
    title: "",
    message: "",
    type: "info" as "info" | "warning" | "error" | "success"
  });

  // Estado del formulario
  const [formData, setFormData] = useState<FormKpiLogisticaData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    unidadesConfirmadas: "",
    unidadesSolicitadas: "",
    porcentajeEntregasTiempo: "",
    porcentajeRetardos: "",
    porcentajeReprogramaciones: "",
    promedioKmOperacion: "",
    promedioCostoFleteLitro: "",
    promedioCostoFleteOperacion: "",
    pagoSemanalFlete: "",
    pagoSemanalPenalizaciones: "",
    porcentajeRutasCotizadas: "",
    porcentajeTransportistas: ""
  });

  // Inicializar datos del formulario
  useEffect(() => {
    if (editingKpi) {
      // Modo edición
      setFormData({
        id: editingKpi.id,
        year: editingKpi.year,
        weekNumber: editingKpi.weekNumber,
        weekStartDate: editingKpi.weekStartDate,
        weekEndDate: editingKpi.weekEndDate,
        unidadesConfirmadas: editingKpi.unidadesConfirmadas,
        unidadesSolicitadas: editingKpi.unidadesSolicitadas,
        porcentajeEntregasTiempo: editingKpi.porcentajeEntregasTiempo,
        porcentajeRetardos: editingKpi.porcentajeRetardos,
        porcentajeReprogramaciones: editingKpi.porcentajeReprogramaciones,
        promedioKmOperacion: editingKpi.promedioKmOperacion,
        promedioCostoFleteLitro: editingKpi.promedioCostoFleteLitro,
        promedioCostoFleteOperacion: editingKpi.promedioCostoFleteOperacion,
        pagoSemanalFlete: editingKpi.pagoSemanalFlete,
        pagoSemanalPenalizaciones: editingKpi.pagoSemanalPenalizaciones,
        porcentajeRutasCotizadas: editingKpi.porcentajeRutasCotizadas,
        porcentajeTransportistas: editingKpi.porcentajeTransportistas
      });
      setShowWeekSelector(false);
    } else if (isAddingOldWeek) {
      // Modo agregar semana anterior
      const weeks: WeekInfo[] = [];
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      
      // Generar semanas desde la semana 1 hasta la semana actual
      for (let week = 1; week <= 53; week++) {
        const weekInfo = getWeekInfo(currentYear, week);
        if (weekInfo.endDate <= currentDate) {
          // Verificar si ya existe un KPI para esta semana
          const existsKpi = existingKpis.some(kpi => 
            kpi.year === weekInfo.year && kpi.weekNumber === weekInfo.weekNumber
          );
          if (!existsKpi) {
            weeks.push(weekInfo);
          }
        }
      }
      
      setAvailableWeeks(weeks.reverse()); // Mostrar las más recientes primero
      setShowWeekSelector(true);
    } else {
      // Modo agregar nueva semana (semana actual)
      const currentWeekInfo = getLastCompletedWeekInfo();
      if (currentWeekInfo && canAddDataForWeek(currentWeekInfo.year, currentWeekInfo.weekNumber)) {
        setFormData(prev => ({
          ...prev,
          year: currentWeekInfo.year,
          weekNumber: currentWeekInfo.weekNumber,
          weekStartDate: currentWeekInfo.startDate.toISOString(),
          weekEndDate: currentWeekInfo.endDate.toISOString()
        }));
      }
      setShowWeekSelector(false);
    }
  }, [editingKpi, isAddingOldWeek, existingKpis]);

  const handleWeekSelect = (weekInfo: WeekInfo) => {
    setSelectedWeek(weekInfo);
    setFormData(prev => ({
      ...prev,
      year: weekInfo.year,
      weekNumber: weekInfo.weekNumber,
      weekStartDate: weekInfo.startDate.toISOString(),
      weekEndDate: weekInfo.endDate.toISOString()
    }));
  };

  const handleInputChange = (field: keyof FormKpiLogisticaData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const validateForm = (): boolean => {
    // Validar que todos los campos requeridos estén llenos
    const requiredFields: (keyof FormKpiLogisticaData)[] = [
      'unidadesConfirmadas', 'unidadesSolicitadas', 'porcentajeEntregasTiempo',
      'porcentajeRetardos', 'porcentajeReprogramaciones', 'promedioKmOperacion',
      'promedioCostoFleteLitro', 'promedioCostoFleteOperacion', 'pagoSemanalFlete',
      'pagoSemanalPenalizaciones', 'porcentajeRutasCotizadas', 'porcentajeTransportistas'
    ];

    for (const field of requiredFields) {
      const value = formData[field];
      if (value === "" || value === null || value === undefined) {
        setAlertConfig({
          title: "Campos Requeridos",
          message: `Por favor, complete todos los campos requeridos.`,
          type: "warning"
        });
        setShowAlert(true);
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    if (showWeekSelector && !selectedWeek) {
      setAlertConfig({
        title: "Seleccionar Semana",
        message: "Por favor, seleccione una semana antes de continuar.",
        type: "warning"
      });
      setShowAlert(true);
      return;
    }

    setLoading(true);

    try {
      // Convertir strings a números
      const dataToSave: KpiLogisticaData = {
        ...formData,
        unidadesConfirmadas: Number(formData.unidadesConfirmadas),
        unidadesSolicitadas: Number(formData.unidadesSolicitadas),
        porcentajeEntregasTiempo: Number(formData.porcentajeEntregasTiempo),
        porcentajeRetardos: Number(formData.porcentajeRetardos),
        porcentajeReprogramaciones: Number(formData.porcentajeReprogramaciones),
        promedioKmOperacion: Number(formData.promedioKmOperacion),
        promedioCostoFleteLitro: Number(formData.promedioCostoFleteLitro),
        promedioCostoFleteOperacion: Number(formData.promedioCostoFleteOperacion),
        pagoSemanalFlete: Number(formData.pagoSemanalFlete),
        pagoSemanalPenalizaciones: Number(formData.pagoSemanalPenalizaciones),
        porcentajeRutasCotizadas: Number(formData.porcentajeRutasCotizadas),
        porcentajeTransportistas: Number(formData.porcentajeTransportistas)
      };

      await onSave(dataToSave);

      // Mostrar confetti
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      onClose();
    } catch (error) {
      console.error("Error al guardar KPI:", error);
      if (error instanceof Error && (error.message.includes("ZodError") || error.message.includes("must be less than or equal to"))) {
        setAlertConfig({
          title: "Error de validación",
          message: "Los datos ingresados no son válidos. Por favor verifica que todos los valores estén dentro de los rangos permitidos.",
          type: "error"
        });
        setShowAlert(true);
      }
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen) return null;

  const kpiFields = [
    {
      key: "unidadesConfirmadas" as keyof FormKpiLogisticaData,
      title: "UNIDADES CONFIRMADAS",
      label: "Unidades Confirmadas",
      description: "Número total de unidades confirmadas para entrega",
      type: "number",
      min: 0,
      max: 100000,
      step: 1
    },
    {
      key: "unidadesSolicitadas" as keyof FormKpiLogisticaData,
      title: "UNIDADES SOLICITADAS",
      label: "Unidades Solicitadas",
      description: "Número total de unidades solicitadas por los clientes",
      type: "number",
      min: 0,
      max: 100000,
      step: 1
    },
    {
      key: "porcentajeEntregasTiempo" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE ENTREGAS A TIEMPO (%)",
      label: "Porcentaje de Entregas a Tiempo (%)",
      description: "Porcentaje de entregas realizadas dentro del tiempo programado",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "porcentajeRetardos" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE RETARDOS / ESTADÍAS (%)",
      label: "Porcentaje de Retardos / Estadías (%)",
      description: "Porcentaje de entregas con retardos o estadías prolongadas",
      type: "number",
      min: 0,
      max: 500,
      step: 0.1
    },
    {
      key: "porcentajeReprogramaciones" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE REPROGRAMACIONES (%)",
      label: "Porcentaje de Reprogramaciones (%)",
      description: "Porcentaje de entregas que requirieron reprogramación",
      type: "number",
      min: 0,
      max: 500,
      step: 0.1
    },
    {
      key: "promedioKmOperacion" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE KM RECORRIDOS POR OPERACIÓN",
      label: "Promedio de KM Recorridos por Operación",
      description: "Kilómetros promedio recorridos por cada operación de entrega",
      type: "number",
      min: 0,
      max: 10000,
      step: 0.1
    },
    {
      key: "promedioCostoFleteLitro" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE COSTO DE FLETE POR LITRO ($)",
      label: "Promedio de Costo de Flete por Litro ($)",
      description: "Costo promedio de flete por litro transportado",
      type: "number",
      min: 0,
      max: 100,
      step: 0.01
    },
    {
      key: "promedioCostoFleteOperacion" as keyof FormKpiLogisticaData,
      title: "PROMEDIO DE COSTO DE FLETE POR OPERACIÓN ($)",
      label: "Promedio de Costo de Flete por Operación ($)",
      description: "Costo promedio de flete por operación completa",
      type: "number",
      min: 0,
      max: 1000000,
      step: 0.01
    },
    {
      key: "pagoSemanalFlete" as keyof FormKpiLogisticaData,
      title: "PAGO SEMANAL DE FLETE ($)",
      label: "Pago Semanal de Flete ($)",
      description: "Monto total pagado por concepto de flete durante la semana",
      type: "number",
      min: 0,
      max: 10000000,
      step: 0.01
    },
    {
      key: "pagoSemanalPenalizaciones" as keyof FormKpiLogisticaData,
      title: "PAGO SEMANAL POR PENALIZACIONES ($)",
      label: "Pago Semanal por Penalizaciones ($)",
      description: "Monto total pagado por penalizaciones durante la semana",
      type: "number",
      min: 0,
      max: 1000000,
      step: 0.01
    },
    {
      key: "porcentajeRutasCotizadas" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE RUTAS COTIZADAS A TIEMPO (%)",
      label: "Porcentaje de Rutas Cotizadas a Tiempo (%)",
      description: "Porcentaje de rutas que fueron cotizadas dentro del tiempo establecido",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "porcentajeTransportistas" as keyof FormKpiLogisticaData,
      title: "PORCENTAJE DE TRANSPORTISTAS CLASIFICADOS (%)",
      label: "Porcentaje de Transportistas Clasificados (%)",
      description: "Porcentaje de transportistas que han sido clasificados según criterios de desempeño",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    }
  ];

  return (
    <>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {editingKpi ? "Editar KPI de Logística" : "Agregar KPI de Logística"}
              </h2>
              <p className="text-sm text-gray-500 mt-1">
                {editingKpi
                  ? `Semana ${editingKpi.weekNumber}/${editingKpi.year}`
                  : isAddingOldWeek
                    ? "Seleccione una semana anterior para agregar datos"
                    : "Datos para la semana actual"
                }
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
            <div className="p-6">
              {/* Selector de semana para agregar datos anteriores */}
              {showWeekSelector && (
                <div className="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
                  <h3 className="text-sm font-medium text-blue-900 mb-3">
                    Seleccionar Semana
                  </h3>
                  <Select onValueChange={(value) => {
                    const weekInfo = availableWeeks.find(w => `${w.year}-${w.weekNumber}` === value);
                    if (weekInfo) handleWeekSelect(weekInfo);
                  }}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Seleccione una semana..." />
                    </SelectTrigger>
                    <SelectContent>
                      {availableWeeks.map((week) => (
                        <SelectItem key={`${week.year}-${week.weekNumber}`} value={`${week.year}-${week.weekNumber}`}>
                          Semana {week.weekNumber}/{week.year} ({week.startDate.toLocaleDateString()} - {week.endDate.toLocaleDateString()})
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {/* Badge con fecha de la semana seleccionada */}
                  {selectedWeek && (
                    <div className="mt-3 flex">
                      <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                        <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        {selectedWeek.startDate.toLocaleDateString('es-ES', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric'
                        })} - {selectedWeek.endDate.toLocaleDateString('es-ES', {
                          day: '2-digit',
                          month: 'short',
                          year: 'numeric'
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Formulario de KPIs */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                {kpiFields.map((field) => (
                  <div key={field.key} className="space-y-2">
                    <div className="space-y-1">
                      <Tooltip content={field.description}>
                        <h3 className="text-sm font-bold text-gray-600 uppercase tracking-wide cursor-default">
                          {field.title}
                        </h3>
                      </Tooltip>
                    </div>
                    <input
                      type="text"
                      inputMode="decimal"
                      pattern="[0-9]*\.?[0-9]*"
                      placeholder="Ingrese un valor"
                      value={formData[field.key]}
                      onChange={(e) => handleInputChange(field.key, e.target.value)}
                      className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-200 placeholder-gray-400"
                      required
                    />
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 px-6 py-3 bg-gray-50 flex-shrink-0 rounded-b-lg">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-primary focus:border-transparent transition-colors"
            >
              Cancelar
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={loading || (showWeekSelector && !selectedWeek)}
              className="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-lg hover:bg-primary/90 focus:ring-2 focus:ring-primary focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Save className="h-4 w-4" />
              )}
              <span>{loading ? "Guardando..." : editingKpi ? "Actualizar" : "Guardar"}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Alert Dialog */}
      <AlertDialog
        isOpen={showAlert}
        onClose={() => setShowAlert(false)}
        title={alertConfig.title}
        message={alertConfig.message}
        type={alertConfig.type}
      />
    </>
  );
};

export default KpiLogisticaModal;
