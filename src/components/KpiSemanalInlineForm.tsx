"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import confetti from "canvas-confetti";


import { getWeekInfo, canAddDataForWeek, getWeekDataRestrictionMessage, getLastCompletedWeekInfo, type WeekInfo } from "@/lib/utils/weekUtils";
import { type KpiSemanalData } from "@/app/actions/kpis-semanales";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import AlertDialog from "@/components/ui/AlertDialog";

// Interfaz para el formulario que permite valores string o number
interface FormKpiData extends Omit<KpiSemanalData, 'volumenTotalLitros' | 'crecimientoMensual' | 'margenBrutoPorLitro' | 'tasaRetencionClientes' | 'cumplimientoObjetivo' | 'desviacionVentas' | 'cicloPromedioCierre' | 'clientesActivosMensuales'> {
  volumenTotalLitros: number | string;
  crecimientoMensual: number | string;
  margenBrutoPorLitro: number | string;
  tasaRetencionClientes: number | string;
  cumplimientoObjetivo: number | string;
  desviacionVentas: number | string;
  cicloPromedioCierre: number | string;
  clientesActivosMensuales: number | string;
}

// Componente Tooltip personalizado estilo Chart.js
const Tooltip: React.FC<{ children: React.ReactNode; content: string; className?: string }> = ({
  children,
  content,
  className = ""
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState({ top: true, left: false, right: false, center: true });
  const tooltipRef = React.useRef<HTMLDivElement>(null);
  const containerRef = React.useRef<HTMLDivElement>(null);

  const updatePosition = React.useCallback(() => {
    if (!containerRef.current || !tooltipRef.current) return;

    const container = containerRef.current.getBoundingClientRect();
    const tooltip = tooltipRef.current.getBoundingClientRect();
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    };

    // Determinar posición vertical (arriba o abajo)
    const spaceBelow = viewport.height - container.bottom;
    const spaceAbove = container.top;
    const tooltipHeight = tooltip.height || 80; // altura estimada más realista

    const showAbove = spaceBelow < tooltipHeight + 20 && spaceAbove > tooltipHeight + 20;

    // Determinar posición horizontal (centrado, izquierda o derecha)
    const containerCenter = container.left + container.width / 2;
    const tooltipWidth = tooltip.width || 280; // ancho estimado más realista
    const halfTooltipWidth = tooltipWidth / 2;
    const margin = 20; // margen de seguridad

    let horizontalPosition = 'center';
    if (containerCenter - halfTooltipWidth < margin) {
      horizontalPosition = 'left';
    } else if (containerCenter + halfTooltipWidth > viewport.width - margin) {
      horizontalPosition = 'right';
    }

    setPosition({
      top: !showAbove,
      left: horizontalPosition === 'left',
      right: horizontalPosition === 'right',
      center: horizontalPosition === 'center'
    });
  }, []);

  React.useEffect(() => {
    if (isVisible) {
      updatePosition();
    }
  }, [isVisible, updatePosition]);

  const getTooltipClasses = () => {
    let classes = "absolute px-3 py-2 text-xs font-medium text-white rounded-md shadow-lg pointer-events-none transition-opacity duration-200";

    if (position.top) {
      classes += " top-full mt-2";
    } else {
      classes += " bottom-full mb-2";
    }

    if (position.center) {
      classes += " left-1/2 transform -translate-x-1/2";
    } else if (position.left) {
      classes += " left-0";
    } else if (position.right) {
      classes += " right-0";
    }

    return classes;
  };

  const getArrowClasses = () => {
    let classes = "absolute w-2 h-2 transform rotate-45";

    if (position.top) {
      classes += " -top-1";
    } else {
      classes += " -bottom-1";
    }

    if (position.center) {
      classes += " left-1/2 -translate-x-1/2";
    } else if (position.left) {
      classes += " left-3";
    } else if (position.right) {
      classes += " right-3";
    }

    return classes;
  };

  const getArrowStyle = () => {
    return {
      backgroundColor: 'rgba(0, 0, 0, 0.8)',
      border: '1px solid rgba(255, 255, 255, 0.1)'
    };
  };

  return (
    <div
      ref={containerRef}
      className={`relative inline-block ${className}`}
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            zIndex: 9999,
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            backdropFilter: 'blur(4px)',
            border: '1px solid rgba(255, 255, 255, 0.1)',
            maxWidth: '280px',
            width: 'max-content',
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            lineHeight: '1.4'
          }}
        >
          {content}
          <div
            className={getArrowClasses()}
            style={getArrowStyle()}
          ></div>
        </div>
      )}
    </div>
  );
};

interface KpiSemanalInlineFormProps {
  onClose: () => void;
  onSave: (data: KpiSemanalData) => Promise<void>;
  editingKpi?: KpiSemanalData | null;
  isAddingOldWeek?: boolean;
  existingKpis?: KpiSemanalData[];
  onEdit?: (kpi: KpiSemanalData) => void;
}

const KpiSemanalInlineForm: React.FC<KpiSemanalInlineFormProps> = ({
  onClose,
  onSave,
  editingKpi,
  isAddingOldWeek = false,
  existingKpis = [],
  onEdit
}) => {
  const [loading, setLoading] = useState(false);
  const [currentWeek, setCurrentWeek] = useState<WeekInfo | null>(null);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [selectedWeek, setSelectedWeek] = useState<number>(1);
  const [showDuplicateAlert, setShowDuplicateAlert] = useState(false);
  const [showWeekRestrictionAlert, setShowWeekRestrictionAlert] = useState(false);
  const [weekRestrictionMessage, setWeekRestrictionMessage] = useState("");
  const [showValidationErrorAlert, setShowValidationErrorAlert] = useState(false);
  const [validationErrorMessage, setValidationErrorMessage] = useState("");

  // Estados para manejo de duplicados y modo read-only
  const [isReadOnlyMode, setIsReadOnlyMode] = useState(false);
  const [existingData, setExistingData] = useState<KpiSemanalData | null>(null);
  const [hasChanges, setHasChanges] = useState(false);
  const [showSuccessBanner, setShowSuccessBanner] = useState(false);
  const [formData, setFormData] = useState<FormKpiData>({
    year: new Date().getFullYear(),
    weekNumber: 1,
    weekStartDate: "",
    weekEndDate: "",
    volumenTotalLitros: "" as any,
    crecimientoMensual: "" as any,
    margenBrutoPorLitro: "" as any,
    tasaRetencionClientes: "" as any,
    cumplimientoObjetivo: "" as any,
    desviacionVentas: "" as any,
    cicloPromedioCierre: "" as any,
    clientesActivosMensuales: "" as any
  });

  // Función para verificar si ya existen datos para la semana
  const checkExistingData = (year: number, weekNumber: number) => {
    const existing = existingKpis.find(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );

    if (existing && !editingKpi) {
      setExistingData(existing);
      setIsReadOnlyMode(true);
      setShowSuccessBanner(true);
      // Pre-llenar el formulario con datos existentes
      setFormData({
        year: existing.year,
        weekNumber: existing.weekNumber,
        weekStartDate: existing.weekStartDate,
        weekEndDate: existing.weekEndDate,
        volumenTotalLitros: existing.volumenTotalLitros,
        crecimientoMensual: existing.crecimientoMensual,
        margenBrutoPorLitro: existing.margenBrutoPorLitro,
        tasaRetencionClientes: existing.tasaRetencionClientes,
        cumplimientoObjetivo: existing.cumplimientoObjetivo,
        desviacionVentas: existing.desviacionVentas,
        cicloPromedioCierre: existing.cicloPromedioCierre,
        clientesActivosMensuales: existing.clientesActivosMensuales
      });
    } else {
      setIsReadOnlyMode(false);
      setExistingData(null);
      setShowSuccessBanner(false);
    }
  };

  // Inicializar datos del formulario
  useEffect(() => {
    if (editingKpi) {
      // Modo edición
      setFormData({
        year: editingKpi.year,
        weekNumber: editingKpi.weekNumber,
        weekStartDate: editingKpi.weekStartDate,
        weekEndDate: editingKpi.weekEndDate,
        volumenTotalLitros: editingKpi.volumenTotalLitros,
        crecimientoMensual: editingKpi.crecimientoMensual,
        margenBrutoPorLitro: editingKpi.margenBrutoPorLitro,
        tasaRetencionClientes: editingKpi.tasaRetencionClientes,
        cumplimientoObjetivo: editingKpi.cumplimientoObjetivo,
        desviacionVentas: editingKpi.desviacionVentas,
        cicloPromedioCierre: editingKpi.cicloPromedioCierre,
        clientesActivosMensuales: editingKpi.clientesActivosMensuales
      });

      const weekInfo = getWeekInfo(editingKpi.year, editingKpi.weekNumber);
      setCurrentWeek(weekInfo);
      setSelectedYear(editingKpi.year);
      setSelectedWeek(editingKpi.weekNumber);

      // En modo edición, no verificar duplicados
      setIsReadOnlyMode(false);
      setShowSuccessBanner(false);
    } else {
      // Para nueva entrada de datos, usar la última semana completada
      try {
        const weekInfo = getLastCompletedWeekInfo();
        setCurrentWeek(weekInfo);
        setSelectedYear(weekInfo.year);
        setSelectedWeek(weekInfo.weekNumber);

        // Verificar si ya existen datos para esta semana
        checkExistingData(weekInfo.year, weekInfo.weekNumber);

        setFormData({
          year: weekInfo.year,
          weekNumber: weekInfo.weekNumber,
          weekStartDate: weekInfo.startDate.toISOString(),
          weekEndDate: weekInfo.endDate.toISOString(),
          volumenTotalLitros: "" as any,
          crecimientoMensual: "" as any,
          margenBrutoPorLitro: "" as any,
          tasaRetencionClientes: "" as any,
          cumplimientoObjetivo: "" as any,
          desviacionVentas: "" as any,
          cicloPromedioCierre: "" as any,
          clientesActivosMensuales: "" as any
        });
      } catch (error) {
        console.error("Error al inicializar última semana completada:", error);
      }
    }
  }, [editingKpi, isAddingOldWeek]);

  const handleInputChange = (field: keyof FormKpiData, value: string) => {
    const newFormData = {
      ...formData,
      [field]: value
    };
    setFormData(newFormData);

    // Detectar cambios comparando todos los campos con los datos existentes
    if (existingData) {
      const hasAnyChange = Object.keys(newFormData).some(key => {
        if (key === 'year' || key === 'weekNumber' || key === 'weekStartDate' || key === 'weekEndDate') {
          return false; // Ignorar campos de metadatos
        }
        const formValue = String(newFormData[key as keyof FormKpiData]);
        const existingValue = String(existingData[key as keyof KpiSemanalData]);
        return formValue !== existingValue;
      });
      setHasChanges(hasAnyChange);
    } else {
      setHasChanges(true); // Si no hay datos existentes, siempre hay cambios
    }
  };

  // Función para manejar cambio de año
  const handleYearChange = (year: number) => {
    // Validar que year sea un número válido
    if (isNaN(year) || year < 1900 || year > 2100) {
      console.error("Año inválido:", year);
      return;
    }

    setSelectedYear(year);
    try {
      const weekInfo = getWeekInfo(year, selectedWeek);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      // Usar semana 1 como fallback
      const fallbackWeekInfo = getWeekInfo(year, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        year: year,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para manejar cambio de semana
  const handleWeekChange = (week: number) => {
    // Validar que week sea un número válido
    if (isNaN(week) || week < 1 || week > 53) {
      console.error("Número de semana inválido:", week);
      return;
    }

    setSelectedWeek(week);
    try {
      const weekInfo = getWeekInfo(selectedYear, week);
      setCurrentWeek(weekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: week,
        weekStartDate: weekInfo.startDate.toISOString(),
        weekEndDate: weekInfo.endDate.toISOString()
      }));
    } catch (error) {
      console.error("Error al obtener información de la semana:", error);
      // Usar semana 1 como fallback
      const fallbackWeekInfo = getWeekInfo(selectedYear, 1);
      setSelectedWeek(1);
      setCurrentWeek(fallbackWeekInfo);
      setFormData(prev => ({
        ...prev,
        weekNumber: 1,
        weekStartDate: fallbackWeekInfo.startDate.toISOString(),
        weekEndDate: fallbackWeekInfo.endDate.toISOString()
      }));
    }
  };

  // Función para calcular días restantes para el próximo ingreso
  const getDaysUntilNextEntry = () => {
    if (!currentWeek) return null;

    const today = new Date();
    const weekEnd = new Date(currentWeek.endDate);
    const nextWeekStart = new Date(weekEnd);
    nextWeekStart.setDate(weekEnd.getDate() + 1);

    const diffTime = nextWeekStart.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    return diffDays > 0 ? diffDays : 0;
  };

  // Función para verificar si ya existe un KPI para la semana seleccionada
  const checkDuplicateKpi = (year: number, weekNumber: number): boolean => {
    if (editingKpi) return false; // Si estamos editando, no verificar duplicados

    return existingKpis.some(kpi =>
      kpi.year === year && kpi.weekNumber === weekNumber
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Verificar si ya existe un KPI para esta semana
    if (checkDuplicateKpi(formData.year, formData.weekNumber)) {
      setShowDuplicateAlert(true);
      return;
    }

    // Verificar si se puede agregar datos para esta semana (solo para nuevos KPIs, no para edición)
    if (!editingKpi && !canAddDataForWeek(formData.year, formData.weekNumber)) {
      setWeekRestrictionMessage(getWeekDataRestrictionMessage(formData.year, formData.weekNumber));
      setShowWeekRestrictionAlert(true);
      return;
    }

    setLoading(true);
    try {
      // Procesar datos del formulario
      const processedData: KpiSemanalData = {
        year: formData.year,
        weekNumber: formData.weekNumber,
        weekStartDate: formData.weekStartDate,
        weekEndDate: formData.weekEndDate,
        volumenTotalLitros: formData.volumenTotalLitros === "" || formData.volumenTotalLitros === null ? 0 : Number(formData.volumenTotalLitros),
        crecimientoMensual: formData.crecimientoMensual === "" || formData.crecimientoMensual === null ? 0 : Number(formData.crecimientoMensual),
        margenBrutoPorLitro: formData.margenBrutoPorLitro === "" || formData.margenBrutoPorLitro === null ? 0 : Number(formData.margenBrutoPorLitro),
        tasaRetencionClientes: formData.tasaRetencionClientes === "" || formData.tasaRetencionClientes === null ? 0 : Number(formData.tasaRetencionClientes),
        cumplimientoObjetivo: formData.cumplimientoObjetivo === "" || formData.cumplimientoObjetivo === null ? 0 : Number(formData.cumplimientoObjetivo),
        desviacionVentas: formData.desviacionVentas === "" || formData.desviacionVentas === null ? 0 : Number(formData.desviacionVentas),
        cicloPromedioCierre: formData.cicloPromedioCierre === "" || formData.cicloPromedioCierre === null ? 0 : Number(formData.cicloPromedioCierre),
        clientesActivosMensuales: formData.clientesActivosMensuales === "" || formData.clientesActivosMensuales === null ? 0 : Number(formData.clientesActivosMensuales)
      };

      await onSave(processedData);

      // Lanzar confeti después de guardar exitosamente
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });

      onClose();
    } catch (error) {
      console.error("Error al guardar KPI:", error);
      if (error instanceof Error && (error.message.includes("ZodError") || error.message.includes("must be less than or equal to"))) {
        setValidationErrorMessage("Los datos ingresados no son válidos. Por favor verifica que todos los valores estén dentro de los rangos permitidos.");
        setShowValidationErrorAlert(true);
      }
    } finally {
      setLoading(false);
    }
  };

  const kpiFields = [
    {
      key: "volumenTotalLitros" as keyof FormKpiData,
      title: "VOLUMEN TOTAL DE VENTA POR MES (L)",
      label: "Volumen Total de Venta por Mes (Litros)",
      description: "Suma total de litros vendidos en el mes",
      type: "number",
      min: 0,
      max: 100000000,
      step: 1
    },
    {
      key: "crecimientoMensual" as keyof FormKpiData,
      title: "CRECIMIENTO MENSUAL DE VENTAS (%)",
      label: "Crecimiento Mensual de Ventas (%)",
      description: "[(Ventas mes actual - Ventas mes anterior) / Ventas mes anterior] * 100",
      type: "number",
      min: -100000000,
      max: 100000000,
      step: 0.1
    },
    {
      key: "margenBrutoPorLitro" as keyof FormKpiData,
      title: "MARGEN BRUTO POR LITRO VENDIDO",
      label: "Margen Bruto por Litro Vendido",
      description: "Ganancia bruta obtenida por cada litro vendido",
      type: "number",
      min: 0,
      max: 100,
      step: 0.01
    },
    {
      key: "tasaRetencionClientes" as keyof FormKpiData,
      title: "TASA DE RETENCIÓN DE CLIENTES (%)",
      label: "Tasa de Retención de Clientes (%)",
      description: "(Clientes que repiten compra / Total de clientes del período anterior) * 100",
      type: "number",
      min: 0,
      max: 100,
      step: 0.1
    },
    {
      key: "cumplimientoObjetivo" as keyof FormKpiData,
      title: "CUMPLIMIENTO DEL OBJETIVO DE VENTAS (%)",
      label: "Porcentaje de Cumplimiento del Objetivo de Ventas Mensual (%)",
      description: "(Ventas reales / Meta de ventas) * 100",
      type: "number",
      min: 0,
      max: 200,
      step: 0.1
    },
    {
      key: "desviacionVentas" as keyof FormKpiData,
      title: "DESVIACIÓN VENTAS PROYECTADAS VS REALES (%)",
      label: "Desviación entre Ventas Proyectadas y Reales (%)",
      description: "(Ventas reales - Ventas proyectadas) / Ventas proyectadas * 100",
      type: "number",
      min: -100000000,
      max: 100000000,
      step: 0.1
    },
    {
      key: "cicloPromedioCierre" as keyof FormKpiData,
      title: "CICLO PROMEDIO DE CIERRE DE VENTAS (DÍAS)",
      label: "Ciclo Promedio de Cierre de Ventas (Días)",
      description: "Tiempo promedio desde el primer contacto hasta el cierre de la venta",
      type: "number",
      min: 0,
      max: 365,
      step: 1
    },
    {
      key: "clientesActivosMensuales" as keyof FormKpiData,
      title: "CLIENTES ACTIVOS MENSUALES",
      label: "Número de Clientes Activos Mensuales",
      description: "Cantidad de clientes únicos que realizaron al menos una compra en el mes",
      type: "number",
      min: 0,
      max: 100000000,
      step: 1
    }
  ];

  return (
    <>
      <motion.div
        className="space-y-8 p-6"
        initial={{ opacity: 0, y: 8 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -8 }}
        transition={{ duration: 0.2, ease: "easeOut" }}
      >
        {/* Banner de éxito para semana ya registrada */}
        {showSuccessBanner && existingData && (
          <motion.div
            className="bg-green-500/10 border border-green-500/30 rounded-lg p-4 mb-2"
            initial={{ opacity: 0, scale: 0.98 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.15, delay: 0.05 }}
          >
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-green-600">
                  Semana {existingData.weekNumber} registrada ({currentWeek?.startDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })} – {currentWeek?.endDate.toLocaleDateString('es-ES', { day: '2-digit', month: 'short' })})
                </p>
              </div>
            </div>
          </motion.div>
        )}

        {/* Header */}
        <motion.div
          className="mb-6"
          initial={{ opacity: 0, y: 5 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.2, delay: 0.05 }}
        >
          <div className="flex items-center gap-3 mb-2">
            <h2 className="text-xl md:text-xl lg:text-2xl text-black font-medium">
              {editingKpi ? "Editar" : isAddingOldWeek ? "Agregar Semana Antigua" : isReadOnlyMode ? "Datos registrados" : "Agregar nueva semana"}
            </h2>
            {isReadOnlyMode && existingData && onEdit && (
              <button
                onClick={() => onEdit(existingData)}
                className="px-3 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-md hover:bg-primary/10 transition-colors"
                title="Modificar los datos registrados"
              >
                Editar
              </button>
            )}
          </div>

          {/* Badge de semana - mostrar en todos los modos excepto Agregar Semana Antigua */}
          {!isAddingOldWeek && currentWeek && (
            <div className="mb-4">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-xs lg:text-sm font-medium bg-primary/10 text-primary">
                Semana {currentWeek.weekNumber}/{currentWeek.year} ({currentWeek.startDate.toLocaleDateString()} - {currentWeek.endDate.toLocaleDateString()})
              </span>
            </div>
          )}

          <div className="flex items-center gap-2">
            {/* Recordatorio proactivo */}
            {(() => {
              const daysUntilNext = getDaysUntilNextEntry();
              return !isReadOnlyMode && !editingKpi && daysUntilNext !== null && daysUntilNext > 0 ? (
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
                  Próximo ingreso de datos en {daysUntilNext} días
                </span>
              ) : null;
            })()}
          </div>
        </motion.div>

        {/* Form */}
        <motion.form
          onSubmit={handleSubmit}
          className="space-y-6"
          initial={{ opacity: 0, y: 6 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.25, delay: 0.1 }}
        >
          {/* Selectores de Año y Semana para semanas antiguas */}
          {isAddingOldWeek && !editingKpi && (
            <div className="bg-primary/10 rounded-lg p-3">
              <h3 className="text-sm font-medium text-blue-800 mb-2">Seleccionar Semana</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                <div className="flex flex-col h-full">
                  <label className="block text-sm font-medium text-blue-800 mb-1">
                    Año
                  </label>
                  <Select
                    value={selectedYear.toString()}
                    onValueChange={(value) => {
                      const year = parseInt(value);
                      if (!isNaN(year)) {
                        handleYearChange(year);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                      <SelectValue placeholder="Seleccione un año" />
                    </SelectTrigger>
                    <SelectContent
                      position="popper"
                      side="bottom"
                      align="start"
                      sideOffset={4}
                      className="z-[9999] max-h-[200px] overflow-y-auto"
                    >
                      {Array.from({ length: new Date().getFullYear() - 2021 + 1 }, (_, i) => {
                        const year = new Date().getFullYear() - i;
                        return (
                          <SelectItem key={year} value={year.toString()}>
                            {year}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex flex-col h-full">
                  <label className="block text-sm font-medium text-blue-800 mb-1">
                    Semana
                  </label>
                  <Select
                    value={selectedWeek.toString()}
                    onValueChange={(value) => {
                      const week = parseInt(value);
                      if (!isNaN(week)) {
                        handleWeekChange(week);
                      }
                    }}
                  >
                    <SelectTrigger className="w-full border border-gray-300 focus:ring-2 focus:ring-primary focus:border-transparent h-10">
                      <SelectValue placeholder="Seleccione una semana" />
                    </SelectTrigger>
                    <SelectContent
                      position="popper"
                      side="bottom"
                      align="start"
                      sideOffset={4}
                      className="z-[9999] max-h-[200px] overflow-y-auto"
                    >
                      {Array.from({ length: 52 }, (_, i) => {
                        const week = i + 1;
                        return (
                          <SelectItem key={week} value={week.toString()}>
                            Semana {week}
                          </SelectItem>
                        );
                      })}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              {/* Badge con fecha de la semana seleccionada */}
              {currentWeek && (
                <div className="mt-3 flex">
                  <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 border border-blue-200">
                    <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {currentWeek.startDate.toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })} - {currentWeek.endDate.toLocaleDateString('es-ES', {
                      day: '2-digit',
                      month: 'short',
                      year: 'numeric'
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
            {kpiFields.map((field, index) => (
              <motion.div
                key={field.key}
                className="space-y-2"
                initial={{ opacity: 0, y: 4 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.2, delay: 0.15 + (index * 0.02) }}
              >
                <div className="space-y-1">
                  <Tooltip content={field.description}>
                    <label className="font-medium peer-disabled:cursor-not-allowed peer-disabled:opacity-70 text-gray-600 text-md cursor-default">
                      {field.title}
                    </label>
                  </Tooltip>
                </div>
                <input
                  type="text"
                  inputMode="decimal"
                  pattern="[0-9]*\.?[0-9]*"
                  placeholder="Ingrese un valor numérico"
                  value={formData[field.key]}
                  onChange={(e) => handleInputChange(field.key, e.target.value)}
                  className={`flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 md:text-sm text-md ${
                    isReadOnlyMode
                      ? 'disabled:cursor-not-allowed disabled:opacity-50'
                      : ''
                  }`}
                  readOnly={isReadOnlyMode}
                  disabled={isReadOnlyMode}
                  required={!isReadOnlyMode}
                />
              </motion.div>
            ))}
          </div>

          {/* Footer */}
          <motion.div
            className="flex items-center justify-start space-x-4 pt-2"
            initial={{ opacity: 0, y: 4 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.2, delay: 0.3 }}
          >
            <button
              type="button"
              onClick={onClose}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:border-transparent transition-colors"
            >
              {isReadOnlyMode ? "Cerrar" : "Cancelar"}
            </button>

            {!isReadOnlyMode && (
              <button
                type="submit"
                disabled={loading || (!!existingData && !hasChanges)}
                className="inline-flex items-center text-md bg-primary text-white font-medium leading-6 text-center align-middle select-none py-2 px-4 rounded-md transition-all hover:shadow-lg hover:shadow-primary/80 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? "Guardando" : (existingData ? "Actualizar" : "Guardar")}
                {!loading && (
                  <span className="w-4 h-4 ms-1">
                    <svg className="w-full h-full text-white" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink">
                      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
                        <rect id="bound" x="0" y="0" width="24" height="24"></rect>
                        <path d="M3,13.5 L19,12 L3,10.5 L3,3.7732928 C3,3.70255344 3.01501031,3.63261921 3.04403925,3.56811047 C3.15735832,3.3162903 3.45336217,3.20401298 3.70518234,3.31733205 L21.9867539,11.5440392 C22.098181,11.5941815 22.1873901,11.6833905 22.2375323,11.7948177 C22.3508514,12.0466378 22.2385741,12.3426417 21.9867539,12.4559608 L3.70518234,20.6826679 C3.64067359,20.7116969 3.57073936,20.7267072 3.5,20.7267072 C3.22385763,20.7267072 3,20.5028496 3,20.2267072 L3,13.5 Z" id="Combined-Shape" fill="currentcolor"></path>
                      </g>
                    </svg>
                  </span>
                )}
              </button>
            )}
          </motion.div>
        </motion.form>
      </motion.div>

      {/* Alert Dialog para KPI duplicado */}
      <AlertDialog
        isOpen={showDuplicateAlert}
        onClose={() => setShowDuplicateAlert(false)}
        title="Datos ya registrados"
        message={`Los KPIs de la semana ${formData.weekNumber}/${formData.year} ya están registrados. Puedes editarlos usando el botón "Editar" o seleccionar una semana diferente.`}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para restricción de semana */}
      <AlertDialog
        isOpen={showWeekRestrictionAlert}
        onClose={() => setShowWeekRestrictionAlert(false)}
        title="Restricción temporal"
        message={weekRestrictionMessage}
        type="warning"
        buttonText="Entendido"
      />

      {/* Alert Dialog para errores de validación */}
      <AlertDialog
        isOpen={showValidationErrorAlert}
        onClose={() => setShowValidationErrorAlert(false)}
        title="Error de validación"
        message={validationErrorMessage}
        type="error"
        buttonText="Corregir"
      />
    </>
  );
};

export default KpiSemanalInlineForm;
